# 技术选型文档 - 视觉模块

## 1. 技术选型概述

### 1.1 选型原则
- **稳定性优先**: 选择成熟稳定的技术栈
- **性能导向**: 满足实时处理要求
- **可维护性**: 便于后期维护和扩展
- **成本控制**: 平衡功能需求与成本
- **生态完善**: 拥有良好的社区支持

### 1.2 技术架构
采用分层架构设计，确保模块间的低耦合和高内聚：
- **表示层**: Qt GUI界面
- **业务逻辑层**: 视觉处理核心算法
- **数据访问层**: 配置文件和数据管理
- **硬件抽象层**: 相机设备接口

## 2. 核心技术栈

### 2.1 开发语言选型

#### C++ 17
**选择理由**:
- 高性能，适合实时图像处理
- 丰富的视觉库支持（Halcon、OpenCV）
- 良好的硬件接口支持
- 成熟的内存管理机制

**版本要求**: C++ 17标准
**编译器支持**: 
- Visual Studio 2019 (v142) 或更高版本
- GCC 7.0+ (Linux兼容性考虑)
- Clang 5.0+ (跨平台兼容性)

### 2.2 UI框架选型

#### Qt 5.15.2 LTS
**选择理由**:
- 跨平台支持，便于后期移植
- 丰富的控件库，适合工业软件界面
- 优秀的图像显示和处理能力
- 成熟的信号槽机制
- 长期支持版本，稳定性好

**替代方案考虑**:
- **Qt 6.x**: 功能更新但稳定性待验证
- **MFC**: Windows专用，移植性差
- **WPF**: 需要.NET环境，增加复杂度

**许可证**: 开源LGPL v3或商业许可证

### 2.3 视觉处理库选型

#### 主选方案: Halcon 23.11
**选择理由**:
- 工业级视觉算法库，精度高
- 丰富的形状匹配算法
- 优秀的标定算法支持
- 完善的文档和技术支持
- 广泛的工业应用案例

**技术规格**:
- 版本: 23.11 Progress
- 许可证: 商业许可证（需购买）
- 安装路径: G:\MVTec\HALCON-23.11-Progress
- 支持平台: Windows x64, Linux x64

**成本考虑**:
- 开发许可证: ~$3,000/年
- 运行时许可证: ~$1,500/套
- 总体成本较高，但功能强大

#### 备选方案: OpenCV 4.5+
**选择理由**:
- 开源免费，无许可证成本
- 活跃的社区支持
- 丰富的算法库
- 良好的性能优化

**技术规格**:
- 版本: 4.5.0 或更高版本
- 许可证: Apache 2.0（开源）
- 编译选项: WITH_CUDA=ON（GPU加速）

**局限性**:
- 工业级算法相对较少
- 需要更多自研算法
- 精度可能不如Halcon

### 2.4 相机接口选型

#### 多厂商SDK适配策略
**支持厂商**:
1. **海康威视 (Hikvision)**
   - SDK版本: MVS 3.2.0+
   - 接口类型: USB3.0, GigE
   - 特点: 国产化，成本较低

2. **Basler**
   - SDK版本: Pylon 6.3.0+
   - 接口类型: USB3.0, GigE, CoaXPress
   - 特点: 德国品质，稳定性好

3. **大华 (Dahua)**
   - SDK版本: MVSDK 2.1.0+
   - 接口类型: USB3.0, GigE
   - 特点: 性价比高

**统一接口设计**:
```cpp
class ICameraInterface {
public:
    virtual bool Connect(const std::string& deviceId) = 0;
    virtual bool Disconnect() = 0;
    virtual bool CaptureImage(cv::Mat& image) = 0;
    virtual bool SetExposure(double exposure) = 0;
    virtual bool SetGain(double gain) = 0;
};
```

## 3. 开发环境配置

### 3.1 操作系统要求
- **主要平台**: Windows 10/11 Professional (64位)
- **最低版本**: Windows 10 1909 (Build 18363)
- **推荐配置**: Windows 11 22H2

### 3.2 硬件要求

#### 最低配置
- **CPU**: Intel i5-8400 或 AMD Ryzen 5 2600
- **内存**: 8GB DDR4
- **存储**: 256GB SSD
- **显卡**: 集成显卡（支持OpenGL 3.3+）

#### 推荐配置
- **CPU**: Intel i7-10700K 或 AMD Ryzen 7 3700X
- **内存**: 16GB DDR4 3200MHz
- **存储**: 512GB NVMe SSD
- **显卡**: NVIDIA GTX 1660 或更高（CUDA支持）

### 3.3 开发工具链

#### Visual Studio 2022 Community/Professional
**配置要求**:
- 工作负载: C++桌面开发
- 组件: CMake工具、Git工具
- 扩展: Qt Visual Studio Tools

#### CMake 3.20+
**用途**: 跨平台构建系统
**配置**: 支持Qt、Halcon、OpenCV的查找脚本

#### Git 2.30+
**用途**: 版本控制
**配置**: 支持LFS（大文件存储）

## 4. 第三方依赖管理

### 4.1 包管理策略

#### vcpkg (推荐)
**优势**:
- Microsoft官方支持
- 与Visual Studio集成良好
- 支持多种库的预编译版本

**配置示例**:
```bash
vcpkg install opencv4[contrib,cuda]:x64-windows
vcpkg install qt5-base:x64-windows
vcpkg install spdlog:x64-windows
```

#### Conan (备选)
**优势**:
- 更灵活的依赖管理
- 支持自定义包
- 跨平台支持好

### 4.2 关键依赖库

#### 日志库: spdlog 1.10+
**选择理由**:
- 高性能异步日志
- 丰富的格式化选项
- 线程安全

#### JSON库: nlohmann/json 3.11+
**选择理由**:
- 现代C++设计
- 易于使用的API
- 良好的性能

#### 单元测试: Google Test 1.12+
**选择理由**:
- 功能完善的测试框架
- 良好的IDE集成
- 丰富的断言宏

## 5. 性能优化策略

### 5.1 多线程设计
- **UI线程**: 界面响应和用户交互
- **图像处理线程**: 视觉算法计算
- **相机采集线程**: 图像数据获取
- **文件IO线程**: 配置和数据读写

### 5.2 内存管理
- **智能指针**: 使用std::shared_ptr和std::unique_ptr
- **内存池**: 预分配图像缓冲区
- **RAII**: 资源获取即初始化原则

### 5.3 GPU加速
- **CUDA支持**: 利用NVIDIA GPU加速
- **OpenCL**: 跨平台GPU计算
- **Halcon GPU**: 使用Halcon的GPU算子

## 6. 部署与分发

### 6.1 安装包制作
- **工具**: NSIS 3.08 或 Inno Setup 6.2
- **包含内容**: 
  - 主程序可执行文件
  - Qt运行时库
  - Halcon运行时
  - 相机驱动程序
  - 配置文件模板

### 6.2 许可证管理
- **Halcon许可证**: 网络浮动许可证或本地许可证
- **相机SDK**: 免费分发的运行时库
- **Qt**: LGPL开源许可证合规

### 6.3 更新机制
- **自动更新**: 检查版本并下载更新
- **增量更新**: 仅下载变更部分
- **回滚机制**: 更新失败时恢复到上一版本

## 7. 风险评估与应对

### 7.1 技术风险

#### Halcon许可证风险
**风险等级**: 高
**应对策略**:
- 评估开源替代方案（OpenCV + 自研算法）
- 与MVTec协商批量采购折扣
- 考虑按需付费的云端服务模式

#### 相机兼容性风险
**风险等级**: 中
**应对策略**:
- 设计统一的相机抽象接口
- 优先支持主流厂商
- 提供相机适配开发指南

### 7.2 性能风险

#### 实时性要求
**风险等级**: 中
**应对策略**:
- 早期性能基准测试
- 多线程并行处理
- GPU加速算法

#### 内存占用
**风险等级**: 低
**应对策略**:
- 内存使用监控
- 及时释放不用的资源
- 使用内存池技术

## 8. 技术选型总结

### 8.1 核心技术栈确定
- **开发语言**: C++ 17
- **UI框架**: Qt 5.15.2 LTS
- **视觉库**: Halcon 23.11 (主) + OpenCV 4.5+ (辅)
- **构建系统**: CMake 3.20+
- **开发环境**: Visual Studio 2022

### 8.2 关键决策点
1. **Halcon vs OpenCV**: 选择Halcon确保工业级精度
2. **Qt vs 其他UI框架**: Qt提供最佳的跨平台支持
3. **多厂商相机支持**: 通过适配器模式实现统一接口

### 8.3 后续优化方向
1. **性能优化**: GPU加速、多线程优化
2. **功能扩展**: 支持更多视觉算法
3. **平台移植**: Linux版本开发
4. **云端集成**: 支持云端视觉服务
