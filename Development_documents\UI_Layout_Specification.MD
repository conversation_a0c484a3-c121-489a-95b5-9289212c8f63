# 主窗口UI布局规范

## 1. 整体布局
采用**Dock窗口+中心区域**的布局方式，符合工业软件操作习惯。

## 2. 功能分区

### 2.1 顶部工具栏
- **文件操作区**: 新建/打开/保存项目
- **相机控制区**: 连接/断开相机、拍照按钮
- **标定功能区**: 标定流程控制按钮
- **模板功能区**: 模板创建/匹配按钮

### 2.2 左侧Dock窗口
- **相机参数面板**: 曝光时间、增益等调节
- **标定参数面板**: 标定点管理

### 2.3 右侧Dock窗口
- **模板参数面板**: 匹配参数设置
- **结果输出面板**: 坐标数据显示

### 2.4 中心区域
- **图像显示区**: 显示相机图像和视觉处理结果
- **状态栏**: 显示系统状态和操作提示

## 3. 控件规格

| 控件类型 | 尺寸 | 交互方式 | 备注 |
|---------|------|---------|------|
| 工具栏按钮 | 32x32px | 点击 | 带文字标签 |
| 参数滑块 | 150x20px | 拖动+数值输入 | 双精度浮点 |
| 图像显示区 | 640x480px | 缩放/平移 | 保持宽高比 |

## 4. 交互流程
1. 相机连接 → 拍照 → 标定 → 模板匹配 → 结果输出
2. 各功能区按流程顺序启用/禁用