# 用户故事 (User Story)

## 1. 用户角色 (User Persona)

- **角色**: 视觉模块测试员
- **背景**: 负责在设备联调前，对上位机视觉软件的各项核心功能进行全面测试，确保其稳定性和准确性。
- **核心目标**: 模拟实际操作流程，完成从相机参数调整、系统标定到工件定位的全过程，验证软件是否满足PRD中定义的需求。

## 2. 用户故事

### 2.1. 故事一：调整相机参数

**作为** 一名视觉模块测试员，
**我想要** 通过软件界面上的滑块或输入框，实时调整相机的曝光时间和增益，
**以便于** 在不同光照条件下，都能获得清晰、无过曝或欠曝的图像，为后续的视觉处理提供高质量的基础。

#### 验收标准 (Acceptance Criteria)

1.  **Given** 相机已连接并处于实时预览模式。
2.  **When** 我拖动“曝光时间”的滑块。
3.  **Then** 图像的亮度应随之实时变化。
4.  **When** 我在“增益”输入框中输入一个1到15之间的数值。
5.  **Then** 图像的亮度应相应改变，且界面能正确显示当前增益值。

### 2.2. 故事二：完成首次系统标定

**作为** 一名视觉模块测试员，
**我想要** 遵循软件引导，先在图像上依次点选三个标定点，然后将机器人移动到相应物理位置进行坐标配对，
**以便于** 系统能自动计算并保存像素坐标与物理坐标的精确映射关系。

#### 验收标准 (Acceptance Criteria)

1.  **Given** 我已经将相机和标定板放置在指定位置。
2.  **When** 我点击“开始标定”按钮，并在实时图像上依次使用鼠标左键点击三个标定圆的中心。
3.  **Then** 每点击一次，系统应记录一个像素坐标，并在界面上显示已记录的点数。
4.  **And When** 我集齐三个点的像素坐标后，点击“确认像素坐标”按钮。
5.  **Then** 程序应结合这三个像素坐标点和预设的标定板物理尺寸（点间距120mm, 160mm），自动计算并优化相机内参，以获得更精确的图像到物理世界的映射前提。
6.  **And When** 界面提示我将机器人末端执行器移动到第一个标定点，我移动完成后，在输入框中记录下机器人示教器显示的物理坐标，并点击“记录物理坐标1”。
7.  **And** 我重复此操作，直到记录完所有三个标定点的物理位置。
8.  **And** 我点击“计算并保存标定”按钮。
9.  **Then** 软件界面应提示“标定成功，映射关系已保存”。
10. **And** 当我关闭并重新打开软件时，系统应能自动加载上次的标定结果，无需重新标定。

### 2.3. 故事三：创建并测试模板匹配

**作为** 一名视觉模块测试员，
**我想要** 在软件中对一个标准工件进行拍照，创建其形状模板，并测试在不同位置和角度下软件是否都能准确识别，
**以便于** 验证模板匹配功能的鲁棒性和精度。

#### 验收标准 (Acceptance Criteria)

1.  **Given** 我已完成系统标定，并将一个标准工件放置在相机视野内。
2.  **When** 我拍取一张工件的清晰图像，并在图像上通过拖拽矩形框来定义模板区域。
3.  **And** 我点击“创建模板”。
4.  **Then** 系统应在工件的几何中心自动生成一个默认抓取点。
5.  **And When** 我用鼠标拖动这个抓取点到工件的任意位置。
6.  **Then** 抓取点的位置，即工件的坐标点应被成功修改。
7.  **And When** 我点击“保存模板”。
8.  **Then** 系统应提示“模板创建成功”。
9.  **And Given** 我将工件移动到视野内的另一个位置并旋转一定角度。
10. **When** 我在“匹配阈值”输入框设置 `0.8`，在“最大匹配数量”输入框设置 `5`。
11. **And** 我点击“拍照”进行实时匹配。
12. **Then** 软件应能在图像上用绿色轮廓高亮匹配到的工件。
13. **And** 界面右侧的输出区域，应实时显示该工件的物理坐标（X, Y, Angle），其数值应与机器人的实际位置大致相符。