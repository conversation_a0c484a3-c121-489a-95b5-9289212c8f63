# 产品需求文档 (PRD) - 视觉模块

## 1. 项目概述

本项目旨在开发一套集成在机器人上位机软件中的视觉功能模块，所以项目必须提供详细的API文档和使用说明，以方便其他项目的集成。该模块的核心任务是利用工业相机和Halcon视觉库，实现对特定工件的精确定位，并将计算出的目标物理坐标提供给机器人控制系统。程序使用MVVM架构，将UI、逻辑和数据模型分离开，实现解耦和可维护性。

**重要特性**：这是一个可移植的视觉功能模块项目，要求可以通过类和方法访问，实现模块的解耦，或嵌入其他项目。

### 1.1 项目目标

- 提供高精度的工件定位功能（精度±0.1mm）
- 实现快速的模板匹配算法（匹配时间<100ms）
- 支持多种工业相机厂商
- 提供标准化的API接口
- 确保模块的可移植性和可扩展性

### 1.2 应用场景

- 机器人视觉引导抓取
- 工件质量检测
- 自动化装配线定位
- 工业4.0智能制造

## 2. 技术栈与环境要求

### 2.1 核心技术栈

- **开发语言**: C++ 17
- **UI框架**: Qt 5.15.2 或更高版本
- **视觉库**: Halcon 23.11
- **图像处理**: OpenCV 4.5+（辅助）
- **编译器**: Visual Studio 2019/2022
- **构建系统**: CMake 3.16+

### 2.2 系统环境要求

- **操作系统**: Windows 10/11 (64位)
- **内存**: 最低8GB，推荐16GB
- **存储**: 至少2GB可用空间
- **显卡**: 支持OpenGL 3.3+
- **接口**: USB 3.0或GigE网口（相机连接）

### 2.3 第三方依赖

- **Halcon安装路径**: G:\MVTec\HALCON-23.11-Progress
- **Qt安装路径**: 需要配置Qt环境变量
- **相机SDK**: 支持主流厂商（海康威视、大华、Basler等）

## 3. 性能指标

### 3.1 精度要求

- **标定精度**: ±0.1mm
- **重复定位精度**: ±0.05mm
- **角度精度**: ±0.1°

### 3.2 性能要求

- **图像采集速度**: ≥10fps
- **模板匹配速度**: <100ms/次
- **系统响应时间**: <200ms
- **内存占用**: <500MB
- **CPU占用**: <30%（正常工作状态）

## 3. 功能需求

### 3.1. 相机控制 (Camera Control)

- **FR-CAM-001**: 软件需要提供一个按钮，点击后能触发相机进行一次拍照，并在界面上显示采集到的图像。
- **FR-CAM-002**: 软件需要提供滑块或输入框，允许用户实时调整相机的核心参数，包括但不限于：曝光时间、增益。

### 3.2. 视觉标定 (Vision Calibration)

- **FR-CAL-001**: 软件需支持**三点标定法**，以建立图像像素坐标系与机器人物理坐标系之间的映射关系。
- **FR-CAL-002**: 标定需要使用专用的**标定板**，其规格为：包含三个标准圆点，构成一个直角三角形，两直角边长度分别为120mm和160mm。
- **FR-CAL-003**: 标定流程应引导用户依次完成以下步骤：
    1. 在图像上依次点选三个标定点的像素坐标。
    2. 程序根据用户提供的像素坐标，并结合预设的标定板物理尺寸（120mm, 160mm），自动计算并优化相机内参。
    3. 引导用户将机器人移动到每个标定点，并记录对应的物理坐标。
    4. 集齐三组配对坐标后，点击“计算”按钮，程序自动生成并应用一个2D变换矩阵 (`HomMat2D`)。
- **FR-CAL-004**: 标定成功后，变换矩阵需要能够被保存到本地文件，并在程序启动时自动加载。

### 3.3. 模板创建与匹配 (Template Matching)

- **FR-TMP-001**: **模板创建**
    1. 用户应能在一个静态图像上，通过鼠标交互（如拖拽一个矩形框）来定义一个ROI（感兴趣区域）。
    2. 程序根据此ROI，使用Halcon的`create_shape_model`算子创建一个形状模板。
    3. 创建的模板需要能被保存到本地文件。
- **FR-TMP-002**: **模板匹配**
    1. 程序加载已创建的模板文件。
    2. 在实时采集的图像中，使用Halcon的`find_shape_model`算子进行模板匹配。
    3. 匹配结果（如目标的轮廓、中心点）应在图像上高亮显示。

### 3.4. 结果输出 (Result Output)

- **FR-OUT-001**: **坐标输出**
    1. 对于模板匹配到的每一个目标，程序需利用标定好的变换矩阵，将其中心点的像素坐标转换为物理坐标。
    2. 最终的物理坐标（X, Y, Angle）需要清晰地显示在软件界面的文本框或表格中。
